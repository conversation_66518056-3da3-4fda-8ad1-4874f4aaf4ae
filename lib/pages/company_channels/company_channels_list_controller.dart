import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/models/company_channel_model.dart';
import 'package:myrunway/core/models/company_model.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/services/company_channel_service.dart';
import 'package:myrunway/core/services/company_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';

class CompanyChannelsListController extends GetxController {
  final CompanyChannelService _companyChannelService = Get.find<CompanyChannelService>();
  final CompanyService _companyService = Get.find<CompanyService>();
  final AuthService _authService = Get.find<AuthService>();

  // Reactive variables
  final RxList<CompanyChannelModel> _companyChannels = <CompanyChannelModel>[].obs;
  final RxList<CompanyModel> _companies = <CompanyModel>[].obs;
  final RxBool _isLoading = false.obs;
  final RxBool _isLoadingCompanies = false.obs;
  final RxString _error = ''.obs;
  final Rx<CompanyModel?> _selectedCompany = Rx<CompanyModel?>(null);

  // Getters
  List<CompanyChannelModel> get companyChannels => _companyChannels;
  List<CompanyModel> get companies => _companies;
  bool get isLoading => _isLoading.value;
  bool get isLoadingCompanies => _isLoadingCompanies.value;
  String get error => _error.value;
  bool get hasError => _error.value.isNotEmpty;
  CompanyModel? get selectedCompany => _selectedCompany.value;

  // Permission getters
  bool get canManageCompanyChannels => _authService.hasPermission(UserRole.master);
  bool get canCreateChannels => _authService.hasPermission(UserRole.master);
  bool get canEditChannels => _authService.hasPermission(UserRole.master);
  bool get canDeleteChannels => _authService.hasPermission(UserRole.master);

  @override
  void onInit() {
    super.onInit();
    if (canManageCompanyChannels) {
      loadCompanies();
      loadCompanyChannels();
    }
  }

  // Load all companies for filtering
  Future<void> loadCompanies() async {
    _isLoadingCompanies.value = true;
    _error.value = '';

    final response = await _companyService.getCompanies();

    if (response.success && response.data != null) {
      _companies.value = response.data!;
    } else {
      _error.value = response.message ?? 'فشل في جلب قائمة الشركات';
    }

    _isLoadingCompanies.value = false;
  }

  // Load company channels with optional company filter
  Future<void> loadCompanyChannels({int? companyId}) async {
    _isLoading.value = true;
    _error.value = '';

    final response = await _companyChannelService.getCompanyChannels(
      companyId: companyId ?? _selectedCompany.value?.id,
    );

    if (response.success && response.data != null) {
      _companyChannels.value = response.data!;
    } else {
      _error.value = response.message ?? 'فشل في جلب قائمة قنوات الشركات';
      _companyChannels.clear();
    }

    _isLoading.value = false;
  }

  // Filter channels by company
  void filterByCompany(CompanyModel? company) {
    _selectedCompany.value = company;
    loadCompanyChannels();
  }

  // Clear company filter
  void clearFilter() {
    _selectedCompany.value = null;
    loadCompanyChannels();
  }

  // Refresh data
  Future<void> refreshData() async {
    await Future.wait([
      loadCompanies(),
      loadCompanyChannels(),
    ]);
  }

  // Delete a company channel
  Future<void> deleteCompanyChannel(CompanyChannelModel channel) async {
    if (!canDeleteChannels) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية لحذف قنوات الشركات',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    // Show confirmation dialog
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف القناة "${channel.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    // Perform deletion
    _isLoading.value = true;
    final response = await _companyChannelService.deleteCompanyChannel(channel.id!);

    if (response.success) {
      Get.snackbar(
        'تم الحذف',
        'تم حذف القناة بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      // Refresh the list
      await loadCompanyChannels();
    } else {
      Get.snackbar(
        'خطأ في الحذف',
        response.message ?? 'فشل في حذف القناة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isLoading.value = false;
  }

  // Get filtered channels for display
  List<CompanyChannelModel> get filteredChannels {
    if (_selectedCompany.value == null) {
      return _companyChannels;
    }
    
    return _companyChannels
        .where((channel) => channel.company.id == _selectedCompany.value!.id)
        .toList();
  }

  // Search channels by name or WhatsApp number
  List<CompanyChannelModel> searchChannels(String query) {
    if (query.trim().isEmpty) return filteredChannels;
    
    final lowerQuery = query.toLowerCase().trim();
    return filteredChannels.where((channel) {
      return channel.name.toLowerCase().contains(lowerQuery) ||
             channel.channelWhatsappNumber.contains(lowerQuery) ||
             channel.company.name.toLowerCase().contains(lowerQuery);
    }).toList();
  }
}
