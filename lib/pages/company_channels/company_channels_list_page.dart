import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/company_channels/company_channels_list_controller.dart';
import 'package:myrunway/pages/company_channels/company_channel_create_page.dart';
import 'package:myrunway/pages/company_channels/company_channel_edit_page.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/core/models/company_channel_model.dart';
import 'package:myrunway/core/models/company_model.dart';
import 'package:url_launcher/url_launcher.dart';

class CompanyChannelsListPage extends StatelessWidget {
  const CompanyChannelsListPage({super.key});

  @override
  Widget build(BuildContext context) {
    final CompanyChannelsListController controller = Get.put(
      CompanyChannelsListController(),
    );

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('قنوات الشركات'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.refreshData,
          ),
        ],
      ),
      body: Obx(() {
        if (!controller.canManageCompanyChannels) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.lock, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'ليس لديك صلاحية للوصول إلى قنوات الشركات',
                  style: TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            // Filter and Search Section
            _FilterSection(controller: controller),
            
            // Content Section
            Expanded(
              child: controller.isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : controller.hasError
                      ? _ErrorWidget(controller: controller)
                      : _ChannelsList(controller: controller),
            ),
          ],
        );
      }),
      floatingActionButton: Obx(() {
        if (!controller.canCreateChannels) return const SizedBox.shrink();
        
        return FloatingActionButton(
          onPressed: () => Get.to(() => const CompanyChannelCreatePage()),
          backgroundColor: AppColors.primary,
          child: const Icon(Icons.add, color: Colors.white),
        );
      }),
    );
  }
}

class _FilterSection extends StatelessWidget {
  final CompanyChannelsListController controller;

  const _FilterSection({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Company Filter Dropdown
          Obx(() => DropdownButtonFormField<CompanyModel?>(
            value: controller.selectedCompany,
            decoration: InputDecoration(
              labelText: 'تصفية حسب الشركة',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            items: [
              const DropdownMenuItem<CompanyModel?>(
                value: null,
                child: Text('جميع الشركات'),
              ),
              ...controller.companies.map((company) => DropdownMenuItem(
                value: company,
                child: Text(company.name),
              )),
            ],
            onChanged: controller.filterByCompany,
            isExpanded: true,
          )),
          
          if (controller.selectedCompany != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: Text(
                    'عرض قنوات: ${controller.selectedCompany!.name}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                ),
                TextButton(
                  onPressed: controller.clearFilter,
                  child: const Text('إزالة التصفية'),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}

class _ErrorWidget extends StatelessWidget {
  final CompanyChannelsListController controller;

  const _ErrorWidget({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
          const SizedBox(height: 16),
          Text(
            controller.error,
            style: const TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: controller.refreshData,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }
}

class _ChannelsList extends StatelessWidget {
  final CompanyChannelsListController controller;

  const _ChannelsList({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final channels = controller.filteredChannels;
      
      if (channels.isEmpty) {
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.chat_bubble_outline, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                'لا توجد قنوات شركات',
                style: TextStyle(fontSize: 16),
              ),
              SizedBox(height: 8),
              Text(
                'اضغط على زر + لإضافة قناة جديدة',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
            ],
          ),
        );
      }

      return RefreshIndicator(
        onRefresh: controller.refreshData,
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: channels.length,
          itemBuilder: (context, index) {
            final channel = channels[index];
            return _ChannelCard(
              channel: channel,
              controller: controller,
            );
          },
        ),
      );
    });
  }
}

class _ChannelCard extends StatelessWidget {
  final CompanyChannelModel channel;
  final CompanyChannelsListController controller;

  const _ChannelCard({
    required this.channel,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        channel.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        channel.company.name,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleMenuAction(value, context),
                  itemBuilder: (context) => [
                    if (controller.canEditChannels)
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 20),
                            SizedBox(width: 8),
                            Text('تعديل'),
                          ],
                        ),
                      ),
                    if (controller.canDeleteChannels)
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 20, color: Colors.red),
                            SizedBox(width: 8),
                            Text('حذف', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // WhatsApp Number
            InkWell(
              onTap: () => _launchWhatsApp(channel.channelWhatsappNumber),
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.phone, color: Colors.green[600], size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        channel.channelWhatsappNumber,
                        style: TextStyle(
                          color: Colors.green[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    Icon(Icons.open_in_new, size: 16, color: Colors.green[600]),
                  ],
                ),
              ),
            ),
            
            // Notes (if available)
            if (channel.notes != null && channel.notes!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'ملاحظات: ${channel.notes!}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
            
            // Created date
            const SizedBox(height: 8),
            Text(
              'تم الإنشاء: ${_formatDateTime(channel.createdAt)}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleMenuAction(String action, BuildContext context) {
    switch (action) {
      case 'edit':
        Get.to(() => CompanyChannelEditPage(channel: channel));
        break;
      case 'delete':
        controller.deleteCompanyChannel(channel);
        break;
    }
  }

  void _launchWhatsApp(String phoneNumber) async {
    final Uri whatsappUri = Uri.parse('https://wa.me/$phoneNumber');
    if (await canLaunchUrl(whatsappUri)) {
      await launchUrl(whatsappUri, mode: LaunchMode.externalApplication);
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }
}
