import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/company_channels/company_channels_list_controller.dart';
import 'package:myrunway/pages/company_channels/company_channel_create_page.dart';
import 'package:myrunway/pages/company_channels/company_channel_edit_page.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/core/models/company_channel_model.dart';
import 'package:myrunway/core/models/company_model.dart';
import 'package:url_launcher/url_launcher.dart';

class CompanyChannelsListPage extends StatelessWidget {
  const CompanyChannelsListPage({super.key});

  @override
  Widget build(BuildContext context) {
    final CompanyChannelsListController controller = Get.put(
      CompanyChannelsListController(),
    );

    return Obx(
      () => Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          title: Text(
            controller.isSelectionMode
                ? 'تم تحديد ${controller.selectedChannelIds.length} قناة'
                : 'قنوات الشركات',
          ),
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading:
              controller.isSelectionMode
                  ? IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: controller.toggleSelectionMode,
                  )
                  : null,
          actions: [
            if (controller.isSelectionMode) ...[
              if (controller.hasSelectedChannels)
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  onPressed:
                      controller.isBulkDeleting
                          ? null
                          : controller.bulkDeleteSelectedChannels,
                ),
              IconButton(
                icon: const Icon(Icons.select_all),
                onPressed: controller.selectAllChannels,
              ),
            ] else ...[
              if (controller.canDeleteChannels &&
                  controller.filteredChannels.isNotEmpty)
                IconButton(
                  icon: const Icon(Icons.checklist),
                  onPressed: controller.toggleSelectionMode,
                  tooltip: 'تحديد متعدد',
                ),
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: controller.refreshData,
              ),
            ],
          ],
        ),
        body: Obx(() {
          if (!controller.canManageCompanyChannels) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.lock, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  Text(
                    'ليس لديك صلاحية للوصول إلى قنوات الشركات',
                    style: TextStyle(fontSize: 16),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // Filter and Search Section
              _FilterSection(controller: controller),

              // Content Section
              Expanded(
                child:
                    controller.isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : controller.hasError
                        ? _ErrorWidget(controller: controller)
                        : _ChannelsList(controller: controller),
              ),
            ],
          );
        }),
        floatingActionButton:
            controller.canCreateChannels
                ? FloatingActionButton(
                  onPressed:
                      () => Get.to(() => const CompanyChannelCreatePage()),
                  backgroundColor: AppColors.primary,
                  child: const Icon(Icons.add, color: Colors.white),
                )
                : null,
      ),
    );
  }
}

class _FilterSection extends StatelessWidget {
  final CompanyChannelsListController controller;

  const _FilterSection({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Search Bar
          TextField(
            controller: controller.searchController,
            decoration: InputDecoration(
              hintText: 'البحث في القنوات...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: Obx(
                () =>
                    controller.searchQuery.isNotEmpty
                        ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: controller.clearSearch,
                        )
                        : const SizedBox.shrink(),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            onChanged: controller.updateSearchQuery,
          ),
          const SizedBox(height: 16),

          // Company Filter Dropdown
          Obx(
            () => DropdownButtonFormField<CompanyModel?>(
              value: controller.selectedCompany,
              decoration: InputDecoration(
                labelText: 'تصفية حسب الشركة',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              items: [
                const DropdownMenuItem<CompanyModel?>(
                  value: null,
                  child: Text('جميع الشركات'),
                ),
                ...controller.companies.map(
                  (company) => DropdownMenuItem(
                    value: company,
                    child: Text(company.name),
                  ),
                ),
              ],
              onChanged: controller.filterByCompany,
              isExpanded: true,
            ),
          ),

          // Sorting Section
          const SizedBox(height: 16),
          _SortingSection(controller: controller),

          // Filter and Search Status
          Obx(() {
            final hasFilters =
                controller.selectedCompany != null ||
                controller.searchQuery.isNotEmpty;
            final resultCount = controller.filteredChannels.length;
            final totalCount = controller.companyChannels.length;

            if (!hasFilters && resultCount == totalCount) {
              return const SizedBox.shrink();
            }

            return Container(
              margin: const EdgeInsets.only(top: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.blue[600], size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      hasFilters
                          ? 'عرض $resultCount من $totalCount قناة'
                          : 'إجمالي $totalCount قناة',
                      style: TextStyle(
                        color: Colors.blue[700],
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  if (hasFilters) ...[
                    TextButton(
                      onPressed: () {
                        controller.clearFilter();
                        controller.clearSearch();
                      },
                      child: const Text('مسح الكل'),
                    ),
                  ],
                ],
              ),
            );
          }),
        ],
      ),
    );
  }
}

class _ErrorWidget extends StatelessWidget {
  final CompanyChannelsListController controller;

  const _ErrorWidget({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
          const SizedBox(height: 16),
          Text(
            controller.error,
            style: const TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: controller.refreshData,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }
}

class _ChannelsList extends StatelessWidget {
  final CompanyChannelsListController controller;

  const _ChannelsList({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final channels = controller.filteredChannels;

      if (channels.isEmpty) {
        final hasFilters =
            controller.selectedCompany != null ||
            controller.searchQuery.isNotEmpty;

        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                hasFilters ? Icons.search_off : Icons.chat_bubble_outline,
                size: 64,
                color: Colors.grey,
              ),
              const SizedBox(height: 16),
              Text(
                hasFilters ? 'لا توجد نتائج للبحث' : 'لا توجد قنوات شركات',
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 8),
              Text(
                hasFilters
                    ? 'جرب تغيير كلمات البحث أو إزالة التصفية'
                    : 'اضغط على زر + لإضافة قناة جديدة',
                style: const TextStyle(fontSize: 14, color: Colors.grey),
                textAlign: TextAlign.center,
              ),
              if (hasFilters) ...[
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    controller.clearFilter();
                    controller.clearSearch();
                  },
                  child: const Text('مسح جميع التصفيات'),
                ),
              ],
            ],
          ),
        );
      }

      return RefreshIndicator(
        onRefresh: controller.refreshData,
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: channels.length,
          itemBuilder: (context, index) {
            final channel = channels[index];
            return _ChannelCard(channel: channel, controller: controller);
          },
        ),
      );
    });
  }
}

class _ChannelCard extends StatelessWidget {
  final CompanyChannelModel channel;
  final CompanyChannelsListController controller;

  const _ChannelCard({required this.channel, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final isSelected = controller.isChannelSelected(channel.id!);

      return Card(
        elevation: 2,
        margin: const EdgeInsets.only(bottom: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        color: isSelected ? Colors.blue[50] : null,
        child: InkWell(
          onTap:
              controller.isSelectionMode
                  ? () => controller.toggleChannelSelection(channel.id!)
                  : null,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // Selection checkbox
                    if (controller.isSelectionMode) ...[
                      Checkbox(
                        value: isSelected,
                        onChanged:
                            (_) =>
                                controller.toggleChannelSelection(channel.id!),
                      ),
                      const SizedBox(width: 8),
                    ],

                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            channel.name,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            channel.company.name,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Menu button (only show when not in selection mode)
                    if (!controller.isSelectionMode)
                      PopupMenuButton<String>(
                        onSelected:
                            (value) => _handleMenuAction(value, context),
                        itemBuilder:
                            (context) => [
                              if (controller.canEditChannels)
                                const PopupMenuItem(
                                  value: 'edit',
                                  child: Row(
                                    children: [
                                      Icon(Icons.edit, size: 20),
                                      SizedBox(width: 8),
                                      Text('تعديل'),
                                    ],
                                  ),
                                ),
                              if (controller.canDeleteChannels)
                                const PopupMenuItem(
                                  value: 'delete',
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.delete,
                                        size: 20,
                                        color: Colors.red,
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        'حذف',
                                        style: TextStyle(color: Colors.red),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                      ),
                  ],
                ),
                const SizedBox(height: 12),

                // WhatsApp Number
                InkWell(
                  onTap: () => _launchWhatsApp(channel.channelWhatsappNumber),
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 8,
                      horizontal: 12,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.green[200]!),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.phone, color: Colors.green[600], size: 20),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            channel.channelWhatsappNumber,
                            style: TextStyle(
                              color: Colors.green[700],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        Icon(
                          Icons.open_in_new,
                          size: 16,
                          color: Colors.green[600],
                        ),
                      ],
                    ),
                  ),
                ),

                // Notes (if available)
                if (channel.notes != null && channel.notes!.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    'ملاحظات: ${channel.notes!}',
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),
                ],

                // Created date
                const SizedBox(height: 8),
                Text(
                  'تم الإنشاء: ${_formatDateTime(channel.createdAt)}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }

  void _handleMenuAction(String action, BuildContext context) {
    switch (action) {
      case 'edit':
        Get.to(() => CompanyChannelEditPage(channel: channel));
        break;
      case 'delete':
        controller.deleteCompanyChannel(channel);
        break;
    }
  }

  void _launchWhatsApp(String phoneNumber) async {
    final Uri whatsappUri = Uri.parse('https://wa.me/$phoneNumber');
    if (await canLaunchUrl(whatsappUri)) {
      await launchUrl(whatsappUri, mode: LaunchMode.externalApplication);
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }
}

class _SortingSection extends StatelessWidget {
  final CompanyChannelsListController controller;

  const _SortingSection({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.sort, color: Colors.grey[600], size: 20),
              const SizedBox(width: 8),
              Text(
                'ترتيب حسب:',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          Obx(
            () => Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _SortButton(
                  label: 'الاسم',
                  sortBy: 'name',
                  controller: controller,
                ),
                _SortButton(
                  label: 'الشركة',
                  sortBy: 'company',
                  controller: controller,
                ),
                _SortButton(
                  label: 'تاريخ الإنشاء',
                  sortBy: 'date',
                  controller: controller,
                ),
                _SortButton(
                  label: 'رقم الواتساب',
                  sortBy: 'whatsapp',
                  controller: controller,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _SortButton extends StatelessWidget {
  final String label;
  final String sortBy;
  final CompanyChannelsListController controller;

  const _SortButton({
    required this.label,
    required this.sortBy,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final isActive = controller.sortBy == sortBy;
      final icon = controller.getSortIcon(sortBy);

      return InkWell(
        onTap: () => controller.setSortBy(sortBy),
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color:
                isActive
                    ? AppColors.primary.withValues(alpha: 0.1)
                    : Colors.white,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: isActive ? AppColors.primary : Colors.grey[300]!,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                label,
                style: TextStyle(
                  color: isActive ? AppColors.primary : Colors.grey[700],
                  fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                  fontSize: 14,
                ),
              ),
              const SizedBox(width: 4),
              Icon(
                icon,
                size: 16,
                color: isActive ? AppColors.primary : Colors.grey[500],
              ),
            ],
          ),
        ),
      );
    });
  }
}
