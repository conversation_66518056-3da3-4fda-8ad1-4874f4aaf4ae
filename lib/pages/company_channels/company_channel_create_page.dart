import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/company_channels/company_channel_create_controller.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/core/models/company_model.dart';

class CompanyChannelCreatePage extends StatelessWidget {
  const CompanyChannelCreatePage({super.key});

  @override
  Widget build(BuildContext context) {
    final CompanyChannelCreateController controller = Get.put(
      CompanyChannelCreateController(),
    );

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('إضافة قناة شركة'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          TextButton(
            onPressed: controller.clearForm,
            child: const Text('مسح'),
          ),
        ],
      ),
      body: Obx(() {
        if (!controller.canCreateChannels) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.lock, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'ليس لديك صلاحية لإنشاء قنوات الشركات',
                  style: TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: controller.formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Form Card
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header
                        Row(
                          children: [
                            Icon(Icons.chat_bubble, color: AppColors.primary),
                            const SizedBox(width: 8),
                            const Text(
                              'معلومات القناة',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),

                        // Company Selection
                        const Text(
                          'الشركة *',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Obx(() => DropdownButtonFormField<CompanyModel>(
                          value: controller.selectedCompany,
                          decoration: InputDecoration(
                            hintText: 'اختر الشركة',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                          ),
                          items: controller.companies.map((company) {
                            return DropdownMenuItem(
                              value: company,
                              child: Text(company.name),
                            );
                          }).toList(),
                          onChanged: controller.setSelectedCompany,
                          validator: (value) {
                            if (value == null) {
                              return 'يجب اختيار شركة';
                            }
                            return null;
                          },
                          isExpanded: true,
                        )),
                        const SizedBox(height: 16),

                        // Channel Name
                        const Text(
                          'اسم القناة *',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextFormField(
                          controller: controller.nameController,
                          decoration: InputDecoration(
                            hintText: 'أدخل اسم القناة',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                          ),
                          validator: controller.validateName,
                          textInputAction: TextInputAction.next,
                        ),
                        const SizedBox(height: 16),

                        // WhatsApp Number
                        const Text(
                          'رقم الواتساب *',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextFormField(
                          controller: controller.whatsappNumberController,
                          decoration: InputDecoration(
                            hintText: 'أدخل رقم الواتساب',
                            prefixText: '+',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                          ),
                          keyboardType: TextInputType.phone,
                          validator: controller.validateWhatsAppNumber,
                          textInputAction: TextInputAction.next,
                        ),
                        const SizedBox(height: 16),

                        // Notes
                        const Text(
                          'ملاحظات (اختياري)',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextFormField(
                          controller: controller.notesController,
                          decoration: InputDecoration(
                            hintText: 'أدخل ملاحظات إضافية',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                          ),
                          maxLines: 3,
                          textInputAction: TextInputAction.done,
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Create Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: controller.isCreating
                        ? null
                        : controller.createCompanyChannel,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: controller.isCreating
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                        : const Text(
                            'إنشاء القناة',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),

                const SizedBox(height: 16),

                // Help Text
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.info, color: Colors.blue[600], size: 20),
                          const SizedBox(width: 8),
                          const Text(
                            'معلومات مهمة',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        '• تأكد من صحة رقم الواتساب قبل الحفظ\n'
                        '• يمكن استخدام القناة لإرسال رسائل الطلبات للعملاء\n'
                        '• اسم القناة يجب أن يكون واضحاً ومميزاً',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }
}
