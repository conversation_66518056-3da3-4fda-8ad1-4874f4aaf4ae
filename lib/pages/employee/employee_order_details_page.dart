import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/employee/employee_order_details_controller.dart';
import 'package:myrunway/pages/employee/widgets/order_status_grid.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/core/models/order_model_new.dart';
import 'package:myrunway/core/models/company_channel_model.dart';
import 'package:url_launcher/url_launcher.dart';

class EmployeeOrderDetailsPage extends StatelessWidget {
  final int orderId;

  const EmployeeOrderDetailsPage({super.key, required this.orderId});

  @override
  Widget build(BuildContext context) {
    final EmployeeOrderDetailsController controller = Get.put(
      EmployeeOrderDetailsController(orderId: orderId),
    );

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('تفاصيل الطلب'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.refreshOrderDetails,
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (controller.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                const SizedBox(height: 16),
                Text(
                  controller.error,
                  style: const TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: controller.refreshOrderDetails,
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        if (controller.orderWithHistory == null) {
          return const Center(child: Text('لم يتم العثور على الطلب'));
        }

        return RefreshIndicator(
          onRefresh: controller.refreshOrderDetails,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Order Basic Information
                _OrderInfoCard(controller: controller),
                const SizedBox(height: 16),

                // Customer Information
                _CustomerInfoCard(controller: controller),
                const SizedBox(height: 16),

                // Pricing Information
                _PricingInfoCard(controller: controller),
                const SizedBox(height: 16),

                // Accept Order Button (only if order is assigned)
                if (controller.showAcceptButton)
                  _AcceptOrderButton(controller: controller),
                if (controller.showAcceptButton) const SizedBox(height: 16),

                // Order Completion Section (only if order is processing)
                if (controller.showCompletionSection)
                  _OrderCompletionSection(controller: controller),
                if (controller.showCompletionSection)
                  const SizedBox(height: 16),

                // WhatsApp Channels Section
                if (controller.companyChannels.isNotEmpty)
                  _WhatsAppChannelsCard(controller: controller),
                if (controller.companyChannels.isNotEmpty)
                  const SizedBox(height: 16),

                // Order Timeline
                _OrderTimelineCard(controller: controller),
              ],
            ),
          ),
        );
      }),
    );
  }
}

class _OrderInfoCard extends StatelessWidget {
  final EmployeeOrderDetailsController controller;

  const _OrderInfoCard({required this.controller});

  @override
  Widget build(BuildContext context) {
    final order = controller.orderWithHistory!.order;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.receipt_long, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'معلومات الطلب',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _InfoRow(
              icon: Icons.tag,
              label: 'رقم الداخلي',
              value: '#${order.id}',
            ),
            _InfoRow(icon: Icons.tag, label: 'رقم الطلب', value: order.code),
            _InfoRow(
              icon: Icons.info,
              label: 'الحالة',
              value: order.orderHandlingStatus.displayName,
              valueColor: order.orderHandlingStatus.color,
            ),
            if (order.orderDeliveryStatus != null)
              _InfoRow(
                icon: Icons.local_shipping,
                label: 'حالة التسليم',
                value: order.orderDeliveryStatus!.name,
              ),
            _InfoRow(
              icon: Icons.calendar_today,
              label: 'تاريخ الإنشاء',
              value: _formatDateTime(order.createdAt),
            ),
            if (order.deliveryDeadlineDate != null)
              _InfoRow(
                icon: Icons.schedule,
                label: 'موعد التسليم',
                value: _formatDateTime(order.deliveryDeadlineDate!),
                valueColor: order.isOverdue ? Colors.red : null,
              ),
            if (order.notes != null && order.notes!.isNotEmpty)
              _InfoRow(icon: Icons.note, label: 'ملاحظات', value: order.notes!),
            _InfoRow(
              icon: order.breakable ? Icons.warning : Icons.check_circle,
              label: 'قابل للكسر',
              value: order.breakable ? 'نعم' : 'لا',
              valueColor: order.breakable ? Colors.orange : Colors.green,
            ),
          ],
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

class _CustomerInfoCard extends StatelessWidget {
  final EmployeeOrderDetailsController controller;

  const _CustomerInfoCard({required this.controller});

  @override
  Widget build(BuildContext context) {
    final order = controller.orderWithHistory!.order;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'معلومات العميل',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _InfoRow(
              icon: Icons.person,
              label: 'اسم العميل',
              value: order.customerName,
            ),
            _InfoRow(
              icon: Icons.phone,
              label: 'رقم الهاتف',
              value: order.customerPhone,
              onTap: () => _makePhoneCall(order.customerPhone),
            ),
            if (order.customerAddress != null &&
                order.customerAddress!.isNotEmpty)
              _InfoRow(
                icon: Icons.location_on,
                label: 'العنوان',
                value: order.customerAddress!,
              ),
            if (order.customerCompany != null)
              _InfoRow(
                icon: Icons.business,
                label: 'الشركة',
                value: order.customerCompany!.name,
              ),
          ],
        ),
      ),
    );
  }

  void _makePhoneCall(String phoneNumber) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    }
  }
}

class _PricingInfoCard extends StatelessWidget {
  final EmployeeOrderDetailsController controller;

  const _PricingInfoCard({required this.controller});

  @override
  Widget build(BuildContext context) {
    final order = controller.orderWithHistory!.order;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.monetization_on, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'معلومات التسعير',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (order.totalPrice != null)
              _InfoRow(
                icon: Icons.monetization_on,
                label: 'إجمالي السعر بالعمولة',
                value: '${order.totalPrice!.toStringAsFixed(2)} جنيه',
                valueColor: Colors.green[600],
              ),
            if (order.specialCommissionRate != null)
              _InfoRow(
                icon: Icons.payment,
                label: 'العمولة',
                value:
                    '${order.specialCommissionRate!.toStringAsFixed(2)} جنيه',
                valueColor: Colors.blue[600],
              ),
            if (order.deliveryCustomerPayment != null)
              _InfoRow(
                icon: Icons.payment,
                label: 'المبلغ المدفوع',
                value:
                    '${order.deliveryCustomerPayment!.toStringAsFixed(2)} جنيه',
                valueColor: Colors.blue[600],
              ),
          ],
        ),
      ),
    );
  }
}

class _InfoRow extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final Color? valueColor;
  final VoidCallback? onTap;

  const _InfoRow({
    required this.icon,
    required this.label,
    required this.value,
    this.valueColor,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
          child: Row(
            children: [
              Icon(icon, size: 20, color: Colors.grey[600]),
              const SizedBox(width: 12),
              Expanded(
                flex: 2,
                child: Text(
                  label,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[700],
                  ),
                ),
              ),
              Expanded(
                flex: 3,
                child: Text(
                  value,
                  style: TextStyle(
                    color: valueColor ?? Colors.black87,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.end,
                ),
              ),
              if (onTap != null) ...[
                const SizedBox(width: 8),
                Icon(Icons.open_in_new, size: 16, color: Colors.grey[600]),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

class _AcceptOrderButton extends StatelessWidget {
  final EmployeeOrderDetailsController controller;

  const _AcceptOrderButton({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.check_circle, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'قبول الطلب',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'اضغط على الزر أدناه لقبول الطلب وبدء العمل عليه',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed:
                    controller.isAccepting ? null : controller.acceptOrder,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child:
                    controller.isAccepting
                        ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                        : const Text(
                          'قبول الطلب',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _OrderCompletionSection extends StatelessWidget {
  final EmployeeOrderDetailsController controller;

  const _OrderCompletionSection({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.task_alt, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'إكمال الطلب',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Order Status Selection
            const Text(
              'اختر حالة التسليم:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),
            OrderStatusGrid(controller: controller),
            const SizedBox(height: 16),

            // Customer Payment Input
            const Text(
              'المبلغ المدفوع من العميل (اختياري):',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: controller.customerPaymentController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                hintText: 'أدخل المبلغ المدفوع',
                suffixText: 'جنيه',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Location Info
            if (controller.currentLocation != null)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.location_on, color: Colors.green[600]),
                    const SizedBox(width: 8),
                    const Text(
                      'تم تحديد الموقع الحالي',
                      style: TextStyle(color: Colors.green),
                    ),
                  ],
                ),
              ),
            if (controller.currentLocation != null) const SizedBox(height: 16),

            // Complete Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed:
                    controller.isCompleting || controller.selectedStatus == null
                        ? null
                        : controller.completeOrder,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child:
                    controller.isCompleting
                        ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                        : const Text(
                          'إكمال الطلب',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _OrderTimelineCard extends StatelessWidget {
  final EmployeeOrderDetailsController controller;

  const _OrderTimelineCard({required this.controller});

  @override
  Widget build(BuildContext context) {
    final orderWithHistory = controller.orderWithHistory!;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.timeline, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'تاريخ الطلب',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Order creation
            _TimelineItem(
              icon: Icons.add_circle,
              title: 'تم إنشاء الطلب',
              time: orderWithHistory.order.createdAt,
              isCompleted: true,
            ),

            // Assignment history
            if (orderWithHistory.hasAssignmentHistory)
              _TimelineItem(
                icon: Icons.assignment_ind,
                title: 'تم تعيين الطلب',
                time: orderWithHistory.latestAssignment!.assignedAt,
                subtitle:
                    'تم التعيين إلى: ${orderWithHistory.latestAssignment!.assignedTo.name}',
                isCompleted: true,
              ),

            // Current status
            _TimelineItem(
              icon: _getStatusIcon(orderWithHistory.order.orderHandlingStatus),
              title: orderWithHistory.order.orderHandlingStatus.displayName,
              time: orderWithHistory.order.updatedAt,
              isCompleted:
                  orderWithHistory.order.orderHandlingStatus ==
                  OrderHandlingStatus.completed,
              isActive:
                  orderWithHistory.order.orderHandlingStatus !=
                  OrderHandlingStatus.completed,
            ),
          ],
        ),
      ),
    );
  }

  IconData _getStatusIcon(OrderHandlingStatus status) {
    switch (status) {
      case OrderHandlingStatus.pending:
        return Icons.pending;
      case OrderHandlingStatus.assigned:
        return Icons.assignment;
      case OrderHandlingStatus.processing:
        return Icons.work;
      case OrderHandlingStatus.completed:
        return Icons.check_circle;
    }
  }
}

class _TimelineItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final DateTime time;
  final String? subtitle;
  final bool isCompleted;
  final bool isActive;

  const _TimelineItem({
    required this.icon,
    required this.title,
    required this.time,
    this.subtitle,
    this.isCompleted = false,
    this.isActive = false,
  });

  @override
  Widget build(BuildContext context) {
    final color =
        isCompleted
            ? Colors.green
            : isActive
            ? AppColors.primary
            : Colors.grey;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: color, width: 2),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(fontWeight: FontWeight.w600, color: color),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    subtitle!,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
                const SizedBox(height: 2),
                Text(
                  _formatDateTime(time),
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

class _WhatsAppChannelsCard extends StatelessWidget {
  final EmployeeOrderDetailsController controller;

  const _WhatsAppChannelsCard({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.chat, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'قنوات الواتساب',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            const Text(
              'اختر قناة لإرسال تفاصيل الطلب عبر الواتساب:',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 12),

            Obx(() {
              if (controller.isLoadingChannels) {
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: CircularProgressIndicator(),
                  ),
                );
              }

              return Wrap(
                spacing: 8,
                runSpacing: 8,
                children:
                    controller.companyChannels.map((channel) {
                      return _ChannelButton(
                        channel: channel,
                        onTap:
                            () => controller.launchWhatsAppWithMessage(channel),
                      );
                    }).toList(),
              );
            }),
          ],
        ),
      ),
    );
  }
}

class _ChannelButton extends StatelessWidget {
  final CompanyChannelModel channel;
  final VoidCallback onTap;

  const _ChannelButton({required this.channel, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.green[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.green[200]!),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.phone, color: Colors.green[600], size: 20),
            const SizedBox(width: 8),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  channel.name,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Colors.green[700],
                  ),
                ),
                Text(
                  channel.channelWhatsappNumber,
                  style: TextStyle(fontSize: 12, color: Colors.green[600]),
                ),
              ],
            ),
            const SizedBox(width: 8),
            Icon(Icons.open_in_new, size: 16, color: Colors.green[600]),
          ],
        ),
      ),
    );
  }
}
