import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/employee/home_controller.dart';
import 'package:myrunway/widgets/employee_stats_card.dart';
import 'package:myrunway/widgets/cards/order_card.dart';

class EmployeeHomePage extends StatelessWidget {
  const EmployeeHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final EmployeeHomeController controller = Get.put(EmployeeHomeController());

    return Scaffold(
      appBar: AppBar(
        title: const Text('طلباتي'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.refreshData,
          ),
        ],
      ),
      body: Obx(
        () =>
            controller.isLoading.value
                ? const Center(child: CircularProgressIndicator())
                : RefreshIndicator(
                  onRefresh: controller.refreshData,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Stats Card Section
                        _buildStatsCard(controller),

                        const SizedBox(height: 24),

                        // Filter Controls
                        _buildFilterControls(controller),

                        const SizedBox(height: 16),

                        // Orders List Section
                        _buildOrdersList(controller),
                      ],
                    ),
                  ),
                ),
      ),
    );
  }

  Widget _buildStatsCard(EmployeeHomeController controller) {
    return EmployeeStatsCard(
      stats: controller.employeeStats.value,
      isLoading: controller.isLoadingStats.value,
      onFilterTap: controller.showDateFilterDialog,
    );
  }

  Widget _buildFilterControls(EmployeeHomeController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'فلترة الطلبات',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              TextButton.icon(
                onPressed: controller.clearFilters,
                icon: const Icon(Icons.clear, size: 16),
                label: const Text('مسح الفلاتر'),
                style: TextButton.styleFrom(foregroundColor: Colors.blue),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Quick Date Filters
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildQuickFilterChip(
                controller,
                'اليوم',
                controller.setTodayFilter,
              ),
              _buildQuickFilterChip(
                controller,
                'أمس',
                controller.setYesterdayFilter,
              ),
              _buildQuickFilterChip(
                controller,
                'غداً',
                controller.setTomorrowFilter,
              ),
              _buildQuickFilterChip(
                controller,
                'هذا الأسبوع',
                controller.setThisWeekFilter,
              ),
              _buildQuickFilterChip(
                controller,
                'الأسبوع الماضي',
                controller.setLastWeekFilter,
              ),
              _buildQuickFilterChip(
                controller,
                'هذا الشهر',
                controller.setThisMonthFilter,
              ),
              _buildQuickFilterChip(
                controller,
                'الشهر الماضي',
                controller.setLastMonthFilter,
              ),
              _buildCustomDateFilterChip(controller),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickFilterChip(
    EmployeeHomeController controller,
    String label,
    VoidCallback onTap,
  ) {
    final isActive = controller.isQuickFilterActive(label);
    return ActionChip(
      label: Text(label),
      onPressed: onTap,
      backgroundColor:
          isActive ? Colors.blue.withValues(alpha: 0.1) : Colors.grey[100],
      labelStyle: TextStyle(
        fontSize: 12,
        color: isActive ? Colors.blue : Colors.black87,
        fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
      ),
      side: BorderSide(
        color: isActive ? Colors.blue : Colors.transparent,
        width: 1,
      ),
    );
  }

  Widget _buildCustomDateFilterChip(EmployeeHomeController controller) {
    return Obx(() {
      final hasCustomRange =
          controller.dateFrom.value != null || controller.dateTo.value != null;
      return ActionChip(
        label: Text(hasCustomRange ? 'نطاق مخصص' : 'تاريخ مخصص'),
        onPressed: controller.showDateFilterDialog,
        backgroundColor:
            hasCustomRange
                ? Colors.blue.withValues(alpha: 0.1)
                : Colors.grey[100],
        labelStyle: TextStyle(
          fontSize: 12,
          color: hasCustomRange ? Colors.blue : Colors.black87,
          fontWeight: hasCustomRange ? FontWeight.w600 : FontWeight.normal,
        ),
        side: BorderSide(
          color: hasCustomRange ? Colors.blue : Colors.transparent,
          width: 1,
        ),
      );
    });
  }

  Widget _buildOrdersList(EmployeeHomeController controller) {
    return Obx(() {
      if (controller.isLoadingOrders.value) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.all(32),
            child: CircularProgressIndicator(),
          ),
        );
      }

      if (controller.filteredOrders.isEmpty) {
        return _buildEmptyState();
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'طلباتي (${controller.filteredOrders.length})',
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: controller.filteredOrders.length,
            itemBuilder: (context, index) {
              final order = controller.filteredOrders[index];
              return OrderCard(
                orderNew: order,
                onTap: () => controller.navigateToOrderDetails(order),
              );
            },
          ),
        ],
      );
    });
  }

  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(Icons.assignment_outlined, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'لا توجد طلبات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم العثور على طلبات تطابق الفلاتر المحددة',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
