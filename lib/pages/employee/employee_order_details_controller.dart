import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:geolocator/geolocator.dart';
import 'package:myrunway/core/models/order_history_models.dart';
import 'package:myrunway/core/models/order_model_new.dart';
import 'package:myrunway/core/models/company_channel_model.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/services/order_service.dart';
import 'package:myrunway/core/services/company_channel_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';
import 'package:url_launcher/url_launcher.dart';

class EmployeeOrderDetailsController extends GetxController {
  final OrderService _orderService = Get.find<OrderService>();
  final AuthService _authService = Get.find<AuthService>();
  final CompanyChannelService _companyChannelService =
      Get.find<CompanyChannelService>();

  // Constructor parameter
  final int orderId;

  // Reactive variables
  final Rx<OrderWithHistory?> _orderWithHistory = Rx<OrderWithHistory?>(null);
  final RxList<OrderDeliveryStatus> _orderStatuses =
      <OrderDeliveryStatus>[].obs;
  final RxList<CompanyChannelModel> _companyChannels =
      <CompanyChannelModel>[].obs;
  final RxBool _isLoading = false.obs;
  final RxBool _isAccepting = false.obs;
  final RxBool _isCompleting = false.obs;
  final RxBool _isLoadingStatuses = false.obs;
  final RxBool _isLoadingChannels = false.obs;
  final RxString _error = ''.obs;

  // Form controllers for completion
  final TextEditingController customerPaymentController =
      TextEditingController();
  final Rx<OrderDeliveryStatus?> _selectedStatus = Rx<OrderDeliveryStatus?>(
    null,
  );
  final Rx<Position?> _currentLocation = Rx<Position?>(null);

  EmployeeOrderDetailsController({required this.orderId});

  // Getters
  OrderWithHistory? get orderWithHistory => _orderWithHistory.value;
  List<OrderDeliveryStatus> get orderStatuses => _orderStatuses;
  List<CompanyChannelModel> get companyChannels => _companyChannels;
  bool get isLoading => _isLoading.value;
  bool get isAccepting => _isAccepting.value;
  bool get isCompleting => _isCompleting.value;
  bool get isLoadingStatuses => _isLoadingStatuses.value;
  bool get isLoadingChannels => _isLoadingChannels.value;
  String get error => _error.value;
  bool get hasError => _error.value.isNotEmpty;
  OrderDeliveryStatus? get selectedStatus => _selectedStatus.value;
  Position? get currentLocation => _currentLocation.value;

  // Permission getters
  bool get isEmployee => _authService.currentUser?.role == UserRole.employee;
  bool get canAcceptOrder =>
      isEmployee &&
      orderWithHistory?.order.orderHandlingStatus ==
          OrderHandlingStatus.assigned;
  bool get canCompleteOrder =>
      isEmployee &&
      orderWithHistory?.order.orderHandlingStatus ==
          OrderHandlingStatus.processing;
  bool get showAcceptButton => canAcceptOrder;
  bool get showCompletionSection => canCompleteOrder;

  @override
  void onInit() {
    super.onInit();
    if (!isEmployee) {
      Get.snackbar(
        'خطأ في الصلاحية',
        'ليس لديك صلاحية للوصول إلى هذه الصفحة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      Get.back();
      return;
    }
    loadOrderDetails(orderId);
    _getCurrentLocation();
  }

  @override
  void onClose() {
    customerPaymentController.dispose();
    super.onClose();
  }

  // Load order details with history
  Future<void> loadOrderDetails(int orderId) async {
    _isLoading.value = true;
    _error.value = '';

    final response = await _orderService.getOrderWithHistory(orderId);

    if (response.success && response.data != null) {
      _orderWithHistory.value = response.data!;

      // Load order statuses if order can be completed
      if (canCompleteOrder) {
        await _loadOrderStatuses();
      }

      // Load company channels for WhatsApp integration
      await loadCompanyChannels();
    } else {
      _error.value = response.message ?? 'فشل في جلب تفاصيل الطلب';
      Get.snackbar(
        'خطأ',
        _error.value,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isLoading.value = false;
  }

  // Load available order statuses
  Future<void> _loadOrderStatuses() async {
    if (_orderWithHistory.value?.order.office.id == null) return;

    _isLoadingStatuses.value = true;

    final response = await _orderService.getOrderStatuses(
      _orderWithHistory.value!.order.office.id!,
    );

    if (response.success && response.data != null) {
      _orderStatuses.value = response.data!;
    }

    _isLoadingStatuses.value = false;
  }

  // Get current location
  Future<void> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        return;
      }

      _currentLocation.value = await Geolocator.getCurrentPosition();
    } catch (e) {
      // Location error - continue without location
    }
  }

  // Refresh order details
  Future<void> refreshOrderDetails() async {
    if (_orderWithHistory.value?.order.id != null) {
      await loadOrderDetails(_orderWithHistory.value!.order.id!);
    }
  }

  // Accept order
  Future<void> acceptOrder() async {
    if (!canAcceptOrder) {
      Get.snackbar(
        'خطأ',
        'لا يمكن قبول هذا الطلب في الوقت الحالي',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    _isAccepting.value = true;

    final request = OrderAcceptRequest(
      proofType: 'PROOF_OF_ASSIGNMENT',
      latitude: _currentLocation.value?.latitude,
      longitude: _currentLocation.value?.longitude,
    );

    final response = await _orderService.acceptOrder(orderId, request);

    if (response.success && response.data != null) {
      _orderWithHistory.value = _orderWithHistory.value!.copyWith(
        order: response.data!,
      );

      Get.snackbar(
        'نجح',
        'تم قبول الطلب بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      // Load order statuses for completion workflow
      await _loadOrderStatuses();
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في قبول الطلب',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isAccepting.value = false;
  }

  // Select order status for completion
  void selectOrderStatus(OrderDeliveryStatus status) {
    // TODO: add default status customer payment (rate and fixed options)
    _selectedStatus.value = status;
  }

  // Complete order
  Future<void> completeOrder() async {
    if (!canCompleteOrder) {
      Get.snackbar(
        'خطأ',
        'لا يمكن إكمال هذا الطلب في الوقت الحالي',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    if (_selectedStatus.value == null) {
      Get.snackbar(
        'خطأ',
        'يرجى اختيار حالة التسليم',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    _isCompleting.value = true;

    final request = OrderCompleteRequest(
      latitude: _currentLocation.value?.latitude,
      longitude: _currentLocation.value?.longitude,
      deliveryCustomerPayment:
          customerPaymentController.text.isNotEmpty
              ? double.tryParse(customerPaymentController.text)
              : null,
      orderDeliveryStatus: _selectedStatus.value!.id,
    );

    final response = await _orderService.completeOrder(orderId, request);

    if (response.success && response.data != null) {
      _orderWithHistory.value = _orderWithHistory.value!.copyWith(
        order: response.data!,
      );

      Get.snackbar(
        'نجح',
        'تم إكمال الطلب بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      // Navigate back to orders list
      Get.back();
    } else {
      Get.snackbar(
        'خطأ',
        response.message ?? 'فشل في إكمال الطلب',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    _isCompleting.value = false;
  }

  // Clear completion form
  void clearCompletionForm() {
    customerPaymentController.clear();
    _selectedStatus.value = null;
  }

  // Load company channels for WhatsApp integration
  Future<void> loadCompanyChannels() async {
    if (_orderWithHistory.value?.order.customerCompany?.id == null) return;

    _isLoadingChannels.value = true;

    final response = await _companyChannelService.getChannelsForCompany(
      _orderWithHistory.value!.order.customerCompany!.id!,
    );

    if (response.success && response.data != null) {
      _companyChannels.value = response.data!;
    }

    _isLoadingChannels.value = false;
  }

  // Generate Arabic WhatsApp message template
  String generateWhatsAppMessage() {
    if (_orderWithHistory.value == null) return '';

    final order = _orderWithHistory.value!.order;

    final message = '''
السلام عليكم ورحمة الله وبركاته

تحية طيبة من شركة ${order.customerCompany?.name ?? 'غير محدد'}

تفاصيل الطلب:
🔹 رقم الطلب: ${order.code}
🔹 اسم العميل: ${order.customerName}
🔹 رقم الهاتف: ${order.customerPhone}
🔹 حالة التسليم: ${order.orderHandlingStatus.displayName}
${order.customerAddress != null ? '🔹 العنوان: ${order.customerAddress}' : ''}
${order.notes != null && order.notes!.isNotEmpty ? '🔹 ملاحظات: ${order.notes}' : ''}

معلومات الدفع:
${order.totalPrice != null ? '💰 إجمالي المبلغ: ${order.totalPrice!.toStringAsFixed(2)} جنيه' : ''}
${order.deliveryCustomerPayment != null ? '💳 المبلغ المدفوع: ${order.deliveryCustomerPayment!.toStringAsFixed(2)} جنيه' : ''}

رقم الطلب للمراجعة: #${order.id}

شكراً لتعاملكم معنا
''';

    return message.trim();
  }

  // Launch WhatsApp with message
  Future<void> launchWhatsAppWithMessage(CompanyChannelModel channel) async {
    final message = generateWhatsAppMessage();
    final encodedMessage = Uri.encodeComponent(message);
    final whatsappUrl =
        'https://wa.me/${channel.channelWhatsappNumber}?text=$encodedMessage';

    final uri = Uri.parse(whatsappUrl);

    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      Get.snackbar(
        'خطأ',
        'لا يمكن فتح الواتساب',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
